<template>
  <div class="main" :class='[light == "moon" ? "moon" : "light"]' ref='myImg'>
    <div>
      <div class="time">{{ date }}</div>
      <div class="title2">{{ deptName }}视频快检追朔数据大屏</div>
      <div class="action" :class='[light == "moon" ? "moon" : "light"]'>
        <el-form :inline="true">
          <el-form-item label="统计周期：">
            <el-select :popper-append-to-body="false" v-model="statisticType" placeholder="统计周期：" size="mini"
                       style="width:100px;margin-top:20px" @change="setStatisticType">
              <el-option label="年" value="year"></el-option>
              <el-option label="月" value="month"></el-option>
              <el-option label="日" value="day"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <img src="../../assets/img/sun.png" alt="" class="img1" v-if="light == 'moon'" @click="setLigt('sun')">
        <img src="../../assets/img/sun1.png" alt="" class="img1" v-if="light == 'sun'" @click="setLigt('moon')">
        <img src="../../assets/img/moon.png" alt="" class="img2" v-if="light == 'sun'" @click="setLigt('moon')">
        <img src="../../assets/img/moon1.png" alt="" class="img2" v-if="light == 'moon'" @click="setLigt('sun')">
        <img src="../../assets/img/quanping.png" alt="" class="img3" @click="full">
      </div>
    </div>
    <el-row :gutter="20">
      <el-col :span="6">
        <div class="grid-content">
          <div>
            <el-row :gutter="10">
              <el-col :span="12">
                <div class="total_item">
                 <span class="flex item-center ">
                    <img src="../../assets/img/home/<USER>" alt="">
                    <span class="ml-10">设备总数</span>
                 </span>
                  <span>{{ deviceTotal.deviceTotal }}</span>
                </div>


              </el-col>
              <el-col :span="12">
                <div class="total_item">
                   <span class="flex item-center ">
                      <img src="../../assets/img/home/<USER>" alt="">
                      <span class="ml-10">检测数量</span>
                   </span>
                  <span>{{ deviceTotal.jcsjTotal }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="12">
                <div class="total_item">
                    <span class="flex item-center ">
                      <img src="../../assets/img/home/<USER>" alt="">
                      <span class="ml-10">阳性数量</span>
                    </span>

                  <span style="color:#F38E28"> {{ deviceTotal.yxTotal }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="total_item">
                    <span class="flex item-center ">
                      <img src="../../assets/img/home/<USER>" alt="">
                  <span class="ml-10">已处置量</span>
                    </span>

                  <span>{{ deviceTotal.yxczTotal }}</span>
                </div>
              </el-col>
            </el-row>

            <div class="left2">
              <autoScroll :list="noticeList" :speed="0.9" :waitTime="0" :singleHeight="100"
                          style="height:100px;margin-top:10px;margin-bottom:10px">
                <div v-for="(item, index) in noticeList" :key="index">
                  <div class="leftTitle">
                    <span>{{ item.categoryName }}</span>
                    <span style="margin-left:10px">{{ item.title }}</span>
                  </div>
                </div>
              </autoScroll>
            </div>

            <div>
              <div class="left3">
<!--                <div ref="chart1" style="width:100%;height:230px"></div>-->
                <div class="left4T">市场主体检测量排名</div>
                <div style="width: 90%; height: 70%; margin-left: 5%">
                  <div style="float: right; margin: 10px">
                    <el-button type="primary" size="mini" >总量</el-button>
                    <el-button type="primary" plain  size="mini" >阳性</el-button>
                  </div>


                  <el-table
                    :data="tableData"

                    :cell-style="{ fontSize: '12px' }"
                    style="height: 96%"
                    :row-style="tableRowStyle"
                    :header-cell-style="{
      background: 'rgb(5, 103, 110)',
      color: 'rgb(168, 188, 190)',
      fontSize: '14px'
    }">
                    <el-table-column
                      prop="date"
                      label="排名"
                      width="50"
                     >
                    </el-table-column>
                    <el-table-column
                      prop="name"
                      label="市场"
                      width="160"
                     >
                    </el-table-column>
                    <el-table-column
                      prop="address"
                      width="80"
                      label="检测批次">
                    </el-table-column>
                    <el-table-column
                      prop="address"
                      width="80"
                      label="阳性">
                    </el-table-column>
                  </el-table>
                </div>

              </div>
            </div>


            <div>
              <div class="left4">
                <div style="float: right; margin: 10px">
                  <el-button type="primary" size="mini" >总量</el-button>
                  <el-button type="primary" plain  size="mini" >阳性</el-button>
                </div>
                <div class="left4T">各检测室检测量排名</div>
                <div ref="chart2" style="width:100%;height:390px;margin-top: 10px;"></div>
              </div>
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="12">
        <div>
          <div class="ditu">
            <div ref="chart7" style="width: 100%; height:400px;"></div>
          </div>
        </div>

        <div style="margin-top:5px">
          <el-row :gutter="20">
            <el-col :span="8" v-for="(video, index) in videoList" :key="index">
              <VideoBox :id="'video'+ index"  style="height: 190px" :srcUrl="video.videourl" ></VideoBox>
            </el-col>
          </el-row>
        </div>

        <div class="center">
          <div class="left4T">检测量和阳性量统计</div>
          <div ref="chart8" style="width:100%;height:300px"></div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="grid-content">
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="right1">
                <div class="left4T">抽检不合格/总量对比</div>
                <div ref="chart3" style="width:100%;height:160px;margin-top:10px"></div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="right2">

                <div class="left4T">不同类型市场占比</div>
                <div ref="chart4" style="width:100%;height:180px"></div>
              </div>
            </el-col>

          </el-row>
        </div>


        <div>

          <div class="right2">
            <div class="rightT2" :class='[light == "moon" ? "rightT21" : "rightT22"]'>阳性预警</div>
            <!-- <autoScroll :list="warnList" :speed="0.9" :waitTime="0" :singleHeight="100"
              style="height:150px;margin-top:10px">
              <div class="t-item" v-for="(item, index) in warnList" :key="index">
                <div>
                  <div class="text1">{{ item.title }}</div>
                  <div class="text2">{{ item.name }}</div>

                </div>
              </div>
            </autoScroll> -->

            <autoScroll :list="warnList" :speed="0.9" :waitTime="0" :singleHeight="100"
                        style="height:140px;margin-top:10px">
              <div v-for="(item, index) in warnList" :key="index">
                <span style="color: rgba(238, 158, 4, 1)" class="text1">{{ item.title }}</span>
                <span style="color: rgba(238, 158, 4, 1)" class="text2">{{ item.name }}</span>
              </div>
            </autoScroll>


          </div>
        </div>


        <div class="grid-content">
          <div class="right4">
            <div style="float: right; margin: 10px">
              <el-button type="primary" size="mini" >总量</el-button>
              <el-button type="primary" plain  size="mini" >阳性</el-button>
            </div>
            <div class="left4T">不同类型任务占比</div>
            <div ref="chart5" style="width:100%;height:220px;margin-top:10px"></div>

            <div class="left4T">三级任务AI绩效统计</div>
            <div ref="chart6" style="width:100%;height:220px"></div>
            <div class="tips">
              <div>到岗：567人</div>
              <div style="margin-top:10px">请假：15人</div>
              <div style="margin-top:10px">迟到：32人</div>
            </div>
          </div>

        </div>

      </el-col>
    </el-row>
  </div>
</template>

<script>
import {mapGetters} from "vuex";
import autoScroll from "./scroll.vue";
import {getUserInfo} from "@/api/system/user";
import {
  getCount1, getCount2, getCount3,
  getDept, getNotice,
  getSYjcJlb,
  getSYjcSjb,
  getSYlbJcl,
  getSYlbYxl,
  getSYsbXz,
  getSySph,
  getSYyxYj, getVideo,getCount10
} from "@/api/wei";
import * as echarts from 'echarts';
import screenfull from "screenfull";
import qingdaoMap from "./map.json";
import VideoBox from "@/components/VideoBox.vue";

export default {
  components: {autoScroll, VideoBox},
  name: "wel",
  data() {
    return {
      tableData: [{
        date: '1',
        name: '水渡河农产品市场',
        address: '167',
        number: '2'
      }, {
        date: '2',
        name: '水渡河农产品市场',
        address: '167',
        number: '2'
      },
        {
          date: '3',
          name: '水渡河农产品市场',
          address: '167',
          number: '2'
        }],
      isFullscreen: false,  // default false
      light: 'moon',  // moon mode
      statisticType: 'year', // total type, year\month\day, default year
      firstLoad: true,
      date: '',
      currentDay: '',
      deptName: '',
      today: '',
      realName: '', // user realName
      deviceTotal: {},  // device total data
      chart1: { // chart1
        legend: ['项目一', '项目二'],
        xAxis: ['检测室1', '检测室2', '检测室3', '检测室4', '检测室5', '检测室6'],
        series: [
          {name: '项目一', value: [12, 56, 25, 41, 89, 151]},
          {name: '项目二', value: [6, 85, 14, 25, 36, 14, 88]}
        ]
      },
      chart2: { // chart2
        // yAxis: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Mon']
        yAxis: ['快检室1', '快检室2', '快检室3', '快检室4', '快检室5', '快检室6'],
        series: [
          { name: '快检室1', value: [20, 31, 21, 13, 43, 20, 31, 21, 13, 43] },
          { name: '快检室2', value: [32, 45.67, 34, 34, 32, 45.67, 34, 34] },
          { name: '快检室3', value: [32, 42, 12, 67, 78, 32, 42, 12, 67, 78] },
          { name: '快检室4', value: [32, 48, 35, 34, 34, 32, 48, 35, 34, 34] },
          { name: '快检室5', value: [32, 65, 45, 34, 85, 32, 65, 45, 34, 85] }
        ]
      },
      chart3: { // chart3
        series: {
          name: '数量',
          data: [
            {value: 1048, name: '抽检不合格'},
            {value: 735, name: '总量对比'}
          ]
        }
      },
      chart4: { // chart4
        series: {
          name: 'Access From',
          data: [
            {value: 1048, name: 'Search Engine'},
            {value: 735, name: 'Direct'},
            {value: 580, name: 'Email'},
            {value: 484, name: 'Union Ads'},
            {value: 300, name: 'Video Ads'}
          ]
        }
      },
      chart5: { // chart5
        series: {
          name: '数量',
          data: [
            {value: 1048, name: '农药残留'},
            {value: 735, name: '违禁药品'},
            {value: 580, name: '重金属'},
            {value: 484, name: '瘦肉精'},
            {value: 300, name: '非法添加剂'}
          ]
        }
      },
      chart6: { // chart6
        series: {
          name: 'Access From',
          data: [
            {value: 1048, name: 'Search Engine'},
            {value: 735, name: 'Direct'}
          ]
        }
      },
      chart7: { // chart7
        series: [
          {name: '平度市', count: 120, total: 121},
          {name: '即墨', count: 256, total: 256},
          {name: '城阳', count: 167, total: 167},
          {name: '黄岛', count: 232, total: 232},
          {name: '莱西', count: 232, total: 232},
          {name: '胶州', count: 276, total: 276}
        ]
      },
      chart8: { // chart8
        yAxis: ['22日', '21日', '20日', '21日', '22日'],
        series: [
          {name: '果蔬', value: [132, 132, 32, 132, 124, 214]},
          {name: '水产', value: [32, 41, 321, 123, 231, 68]},
          {name: '生鲜', value: [23, 211, 114, 211, 59, 124]},
          {name: '禽蛋', value: [83, 311, 134, 281, 449, 194]},
          {name: '加工', value: [43, 611, 184, 212, 79, 174]}
        ]
      },
      noticeList: [ // 滚动公告列表, 左上
      ],
      videoList:[],
      warnList: [ // 滚动阳性预警, 右中
        {name: '09:31 5月15日', title: '1北京批发市场 猪肉芹菜重金属阳性猪肉芹菜重金属阳性'},
        {name: '09:31 5月15日', title: '2北京批发市场 猪肉芹菜重金属阳性猪肉芹菜重金属阳性'},
        {name: '09:31 5月15日', title: '3北京批发市场 猪肉芹菜重金属阳性猪肉芹菜重金属阳性'},
        {name: '09:31 5月15日', title: '北京批发市场 猪肉芹菜重金属阳性猪肉芹菜重金属阳性'},
        {name: '09:31 5月15日', title: '北京批发市场 猪肉芹菜重金属阳性猪肉芹菜重金属阳性'},
        {name: '09:31 5月15日', title: '北京批发市场 猪肉芹菜重金属阳性猪肉芹菜重金属阳性'},
        {name: '09:31 5月15日', title: '北京批发市场 猪肉芹菜重金属阳性猪肉芹菜重金属阳性'},
        {name: '09:31 5月15日', title: '北京批发市场 猪肉芹菜重金属阳性猪肉芹菜重金属阳性'},
        {name: '09:31 5月15日', title: '北京批发市场 猪肉芹菜重金属阳性猪肉芹菜重金属阳性'},
        {name: '09:31 5月15日', title: '北京批发市场 猪肉芹菜重金属阳性猪肉芹菜重金属阳性'},
        {name: '09:31 5月15日', title: '北京批发市场 猪肉芹菜重金属阳性猪肉芹菜重金属阳性'}
      ],

      chartList: []  // initNumChart1, unused

    };
  },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  mounted() {
    this.getDate(); // 日期
    this.getTime(); // 时间
    this.getDept(); //标题的部门
    this.getCount1(); // 左边第一部分的统计
    this.getCount2(); // 左边第三部分的统计
    this.getNotice(); // 左边公告
    this.getVideo(); // 三个视频
    // init charts
    // this.initChart1(); // 目标检测完成情况统计
    this.initChart2(); // 各检测时任务排名
    this.initChart3(); // 抽检不合格-总量对比
    this.initChart4(); // 不同类型市场占比
    this.initChart5(); // 不同类型任务占比
    this.initChart6(); // 到岗率统计
    this.initChart7(); // 首屏地图
    this.initChart8(); // 食品安全指数分析
    this.resize();  // 绑定窗口调整事件
    this.getUser(); // 用户信息
    this.fetchSYlbYxl();  // 不同类型任务占比
    this.fetchSYyxYj(); // 滚动阳性预警
    this.getCount10()//
    this.firstLoad = false;
  },
  methods: {
    tableRowStyle({ rowIndex }) {
      return {
        backgroundColor: rowIndex % 2 === 0 ? 'rgb(25, 35, 46)' : 'rgb(5, 103, 110)',
        color: 'rgb(168, 188, 190)'
      }
    },
    resize() {
      // const myChart1 = echarts.getInstanceByDom(this.$refs.chart1);
      const myChart2 = echarts.getInstanceByDom(this.$refs.chart2);
      const myChart3 = echarts.getInstanceByDom(this.$refs.chart3);
      const myChart4 = echarts.getInstanceByDom(this.$refs.chart4);
      const myChart5 = echarts.getInstanceByDom(this.$refs.chart5);
      const myChart6 = echarts.getInstanceByDom(this.$refs.chart6);
      const myChart7 = echarts.getInstanceByDom(this.$refs.chart7);
      const myChart8 = echarts.getInstanceByDom(this.$refs.chart8);

      window.addEventListener("resize", () => {
        // myChart1.resize();
        myChart2.resize();
        myChart3.resize();
        myChart4.resize();
        myChart5.resize();
        myChart6.resize();
        myChart7.resize();
        myChart8.resize();
      });
    },
    getDept() {
      getDept().then(res => {
        this.deptName = res.data.data
      })
    },
    getNotice() {
      getNotice().then(res => {
        this.noticeList = res.data.data.records
      })
    },
    getVideo() {
      getVideo().then(res => {
        console.log(res.data)
        this.videoList = res.data.data
      })
    },
    setStatisticType() {
      this.reloadStatisticType();
    },
    reloadStatisticType() {
      this.getCount1(); // 设备数量
      this.fetchSYlbYxl();  // 不同类型任务占比
      this.fetchSYyxYj();  // 滚动阳性预警
    },
    setLigt(val) {
      this.light = val;
    },
    full() {
      if (screenfull.isEnabled) {
        screenfull.toggle(this.$refs.myImg);
        if (screenfull.isFullscreen) {
          document.documentElement.style.overflow = 'scroll';
        }
      }
    },
    getDate() {
      const now = new Date();
      const year = now.getFullYear();
      const month = now.getMonth() + 1; // 月份从0开始，需要加1
      const day = now.getDate();
      this.date = year + '-' + month + '-' + day;
      // console.log(year, month, day, "aaaaa");
    },
    getTime() {
      const date = new Date();
      const year = date.getFullYear();
      let month = date.getMonth() + 1;
      month = (month > 9) ? month : ("0" + month);
      let day = date.getDate();
      day = (day < 10) ? ("0" + day) : day;
      this.currentDay = day
      this.today = year + "-" + month + "-" + day;
    },
    initChart1() {
      const myChart = echarts.init(this.$refs.chart1);
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999'
            }
          }
        },
        grid: {
          top: '40',
          left: '40',
          bottom: '20',
          right: '20'
        },
        legend: {
          data: this.chart1.legend,
          textStyle: {
            color: 'rgba(204,204,204,0.5)' // 这里设置图例文字的颜色为红色
          }
        },
        xAxis: [
          {
            type: 'category',
            data: this.chart1.xAxis,
            axisPointer: {
              type: 'shadow'
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            splitLine: {
              lineStyle: {
                // 使用深浅的间隔色
                color: ['rgba(4,167,179,0.06)']
              }
            }
          }
        ],
        series: [
          {
            name: this.chart1.series[0].name,
            type: 'bar',
            itemStyle: {
              // 设置柱状图颜色
              color: '#04A7B3'
            },
            data: this.chart1.series[0].value
          },
          {
            name: this.chart1.series[1].name,
            type: 'bar',
            itemStyle: {
              // 设置柱状图颜色
              color: 'rgb(233,137,12)'
            },
            data: this.chart1.series[1].value
          }
        ]
      };
      myChart.setOption(option);
    },

    initChart2() {
      const myChart2 = echarts.init(this.$refs.chart2);
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            // Use axis to trigger tooltip
            type: 'shadow' // 'shadow' as default; can also be 'line' or 'shadow'
          }
        },
        grid: {
          top: '10',
          left: '60',
          bottom: '60',
          right: '20'
        },
        legend: {
          show: false,
          textStyle: {
            color: 'rgba(204,204,204,0.5)' // 这里设置图例文字的颜色为红色
          }
        },
        xAxis: {
          type: 'value',
          splitLine: {
            lineStyle: {
              // 使用深浅的间隔色
              color: ['rgba(4,167,179,0.06)']
            }
          }
        },
        yAxis: {
          type: 'category',
          data: this.chart2.yAxis
        },
        series: [
          {
            name: this.chart2.series[0].name,
            type: 'bar',
            stack: 'total',
            itemStyle: {
              color: '#3f95c2'  // 这里设置柱状图颜色
            },
            label: {
              show: false
            },
            emphasis: {
              focus: 'series'
            },
            data: this.chart2.series[0].value
          },



        ]
      }
      myChart2.setOption(option);
    },

    initChart3() {
      const myChart3 = echarts.init(this.$refs.chart3);
      const option = {
        tooltip: {
          trigger: 'item'
        },
        series: [
          {
            name: this.chart3.series.name,
            type: 'pie',
            data: this.chart3.series.data
          }
        ]
      }
      myChart3.setOption(option);
    },

    initChart4() {
      const myChart4 = echarts.init(this.$refs.chart4);
      const option = {
        tooltip: {
          trigger: 'item'
        },
        series: [
          {
            name: this.chart4.series.name,
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 40,
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: this.chart4.series.data
          }
        ]
      }
      myChart4.setOption(option);
    },

    initChart5() {
      const myChart5 = echarts.init(this.$refs.chart5);
      const option = {
        tooltip: {
          trigger: 'item'
        },
        legend: {
          left: '64%',
          orient: "vertical",
          top: '20%',
          right: 150,
          textStyle: {
            color: 'rgba(204,204,204,0.5)' // 这里设置图例文字的颜色为红色
          }
        },
        series: [
          {
            name: this.chart5.series.name,
            type: 'pie',
            radius: ['40%', '70%'],
            center: ['35%', '50%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 40,
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: this.chart5.series.data
          }
        ]
      }
      myChart5.setOption(option);
    },

    initChart6() {
      const myChart6 = echarts.init(this.$refs.chart6);
      const option = {
        tooltip: {
          trigger: 'item'
        },
        series: [
          {
            name: this.chart6.series.name,
            type: 'pie',
            radius: ['40%', '70%'],
            center: ['35%', '50%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 40,
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: this.chart6.series.data
          }
        ]
      }
      myChart6.setOption(option);
    },

    initChart7() {
      echarts.registerMap("qingdaoMap", qingdaoMap);
      const myChart7 = echarts.init(this.$refs.chart7);
      const option = {
        geo: {
          map: "qingdaoMap",
          label: {
            emphasis: {
              show: false,
              areaColor: "rgba(35, 99, 150,0.1)"
            }
          },
          top: "center",
          left: "center",
          roam: 'move', //禁止拖拽
          selectedMode: "single", //单选
          animationDurationUpdate: 0,
          zoom: 1.5,
          emphasis: {
            label: {
              show: false
            }
          },
          regions: [
            {
              name: "平度市",
              itemStyle: {
                normal: {
                  areaColor: "#3F8EC9"
                },
                emphasis: {
                  areaColor: "#3F8EC9"
                }
              }
            },
            {
              name: "莱西市",
              itemStyle: {
                normal: {
                  areaColor: "#274889"
                },
                emphasis: {
                  areaColor: "#274889"
                }
              }
            },
            {
              name: "胶州市",
              itemStyle: {
                normal: {
                  areaColor: "#274889"
                },
                emphasis: {
                  areaColor: "#274889"
                }
              }
            },
            {
              name: "黄岛区",
              itemStyle: {
                normal: {
                  areaColor: "#274889"
                },
                emphasis: {
                  areaColor: "#274889"
                }
              }
            },
            {
              name: "即墨区",
              itemStyle: {
                normal: {
                  areaColor: "#274889"
                },
                emphasis: {
                  areaColor: "#274889"
                }
              }
            },
            {
              name: "城阳区",
              itemStyle: {
                normal: {
                  areaColor: "#A18D66"
                },
                emphasis: {
                  areaColor: "#A18D66"
                }
              }
            }
          ]
        },
        series: [
          {
            name: "引导线",
            type: "lines",
            coordinateSystem: "geo",
            lineStyle: {
              color: "#fff",
              width: 1,
              opacity: 1,
              type: "solid" // 设置为虚线
            },
            data: [
              {
                coords: [
                  [119.959012, 36.788828], // 平度市位置
                  [119.3559012, 36.7] // 平度市简介位置
                ]
              },
              // 添加莱西市引导线
              {
                coords: [
                  [120.526226, 36.86509], // 莱西市位置
                  [120.8, 36.86] // 莱西市简介位置
                ]
              },
              {
                coords: [
                  [120.006202, 36.285878],
                  [119.6, 36.4]
                ]
              },
              {
                coords: [
                  [119.995518, 35.875138],
                  [119.2, 35.85]
                ]
              },
              {
                coords: [
                  [120.389135, 36.306833],
                  [120.9, 36.05]
                ]
              },
              {
                coords: [
                  [120.447352, 36.390847],
                  [121.2, 36.45]
                ]
              }
            ]
          },
          {
            name: this.chart7.series[0].name,
            type: "scatter",
            coordinateSystem: "geo",
            symbolSize: 0,
            label: {
              show: true,
              position: "center",
              textStyle: {
                color: '#fff', // 标签字体颜色
                fontSize: 18, // 标签字体大小

              },
              formatter: (params) => {
                return `${params.name}\n————\n ${this.chart7.series[0].count}/${this.chart7.series[0].total}`
              }
            },
            markPoint: {
              symbol: 'circle', // 标记点形状
              symbolSize: 20, // 标记点大小
              itemStyle: {
                normal: {
                  color: 'rgb(249,163,14)' // 标记点颜色
                }
              },
              data: [
                {
                  name: '城阳',
                  coord: [120.389135, 36.306833]
                },
                {
                  name: '平度',
                  coord: [119.959012, 36.788828]
                },
                {
                  name: '胶州',
                  coord: [120.006202, 36.285878]
                },
                {
                  name: '黄岛',
                  coord: [119.995518, 35.875138]
                },
                {
                  name: '即墨',
                  coord: [120.447352, 36.390847]
                },
                {
                  name: '莱西',
                  coord: [120.526226, 36.86509]
                }
              ]
            },
            data: [
              {
                name: this.chart7.series[0].name,
                value: [119.0199012, 36.786]
              }
            ]
          },
          {
            name: this.chart7.series[1].name,
            type: "scatter",
            coordinateSystem: "geo",
            symbolSize: 0,
            label: {
              show: true,
              position: "center",
              textStyle: {
                color: '#fff', // 标签字体颜色
                fontSize: 18, // 标签字体大小

              },
              formatter: (params) => {
                return `${params.name}\n————\n ${this.chart7.series[1].count}/${this.chart7.series[1].total}`
              }
            },
            data: [
              {
                name: this.chart7.series[1].name,
                value: [121.20, 36.5355]
              }
            ]
          },
          {
            name: this.chart7.series[2].name,
            type: "scatter",
            coordinateSystem: "geo",
            symbolSize: 0,
            label: {
              show: true,
              position: "center",
              textStyle: {
                color: '#fff', // 标签字体颜色
                fontSize: 18, // 标签字体大小

              },

              formatter: (params) => {
                return `${params.name}\n————\n ${this.chart7.series[2].count}/${this.chart7.series[2].total}`
              }
            },
            data: [
              {
                name: this.chart7.series[2].name,
                value: [120.90, 36.132]
              }
            ]
          },
          {
            name: this.chart7.series[3].name,
            type: "scatter",
            symbolSize: 0,
            coordinateSystem: "geo",
            label: {
              show: true,
              position: "center",
              textStyle: {
                color: '#fff', // 标签字体颜色
                fontSize: 18, // 标签字体大小

              },
              formatter: (params) => {
                return `${params.name}\n————\n ${this.chart7.series[3].count}/${this.chart7.series[3].total}`
              }
            },
            data: [
              {
                name: this.chart7.series[3].name,
                value: [118.87, 35.935]
              }
            ]
          },
          {
            name: this.chart7.series[4].name,
            type: "scatter",
            coordinateSystem: "geo",
            symbolSize: 0,
            label: {
              show: true,
              textStyle: {
                color: '#fff', // 标签字体颜色
                fontSize: 18, // 标签字体大小

              },
              position: "center",
              formatter: (params) => {
                return `${params.name}\n————\n ${this.chart7.series[4].count}/${this.chart7.series[4].total}`
              }
            },
            data: [
              {
                name: this.chart7.series[4].name,
                value: [120.8, 36.945] // 莱西市简介位置
              }
            ]
          },
          {
            name: this.chart7.series[5].name,
            type: "scatter",
            symbolSize: 0,
            coordinateSystem: "geo",
            label: {
              show: true,
              textStyle: {
                color: '#fff', // 标签字体颜色
                fontSize: 18, // 标签字体大小
              },
              position: "center",
              formatter: (params) => {
                return `${params.name}\n————\n ${this.chart7.series[5].count}/${this.chart7.series[5].total}`
              }
            },
            data: [
              {
                name: this.chart7.series[5].name,
                value: [119.26, 36.490]   // 莱西市位置
              }
            ]
          }
        ]
      };
      myChart7.setOption(option);
    },

    dataLines(province, data) {
      const geoCoordMap = {
        市北区: [120.355026, 36.083819],
        市南区: [120.395966, 36.070892],

      };

      var data = {
        市北区: Math.round(Math.random()),
        市南区: Math.round(Math.random()),
      }

      // 保存引导线末端的坐标
      var linesEndCoords = {
        市北区: [geoCoordMap['市北区'][0] + 0.8, geoCoordMap['市北区'][1] + 0.2],
        市南区: [geoCoordMap['市南区'][0] + 0.6, geoCoordMap['市南区'][1] - 0.3],
      }
      var res = [];
      province.forEach(name => {
        res.push({
          name: name,
          value: data[name],
          coords: [
            geoCoordMap[name],
            linesEndCoords[name]
          ],
        })
      })
      return res;
    },

    dataScatter(province, data) {
      const geoCoordMap = {
        市北区: [120.355026, 36.083819],
        市南区: [120.395966, 36.070892],

      };

      var data = {
        市北区: Math.round(Math.random()),
        市南区: Math.round(Math.random()),
      }

      // 保存引导线末端的坐标
      var linesEndCoords = {
        市北区: [geoCoordMap['市北区'][0], geoCoordMap['市北区'][1] + 8],
        市南区: [geoCoordMap['市南区'][0], geoCoordMap['市南区'][1] + 16],
      }
      var res = [];
      province.forEach(name => {
        res.push({
          name: name,
          value: [geoCoordMap[name][0], geoCoordMap[name][1], data[name]]
        })
      })
      return res;

    },


    initChart8() {
      const myChart8 = echarts.init(this.$refs.chart8);
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          top: '5%',
          textStyle: {
            color: 'rgba(204,204,204,0.5)'
          }
        },
        grid: {
          left: '20',
          right: '4%',
          bottom: '6%',
          containLabel: true
        },
        xAxis: {
          type: 'category',  // 改为category类型显示日期
          boundaryGap: [0, 0.01],
          data: this.chart8.yAxis,  // 使用原来的y轴数据作为x轴日期
          splitLine: {
            lineStyle: {
              color: ['rgba(4,167,179,0.06)']
            }
          }
        },
        yAxis: {
          type: 'value',  // 改为value类型显示数量
          splitLine: {
            lineStyle: {
              color: ['rgba(4,167,179,0.06)']
            }
          }
        },
        series: [
          {
            name: this.chart8.series[0].name,
            type: 'line',
            data: this.chart8.series[0].value
          },
          {
            name: this.chart8.series[1].name,
            type: 'line',
            data: this.chart8.series[1].value
          },
          {
            name: this.chart8.series[2].name,
            type: 'line',
            data: this.chart8.series[2].value
          },
          {
            name: this.chart8.series[3].name,
            type: 'line',
            data: this.chart8.series[3].value
          },
          {
            name: this.chart8.series[4].name,
            type: 'line',
            data: this.chart8.series[4].value
          }
        ]
      }
      myChart8.setOption(option);
    },

    getUser() {
      getUserInfo().then(res => {
        const user = res.data.data;
        this.realName = user.realName
        // 用户信息

        // this.form = {
        //   id: user.id,
        //   avatar: user.avatar,
        //   name: user.name,
        //   realName: user.realName,
        //   phone: user.phone,
        //   email: user.email,
        // }
      });
    },
    getCount1() {
      let params = {type: this.statisticType};
      getCount1(params).then(res => {
        this.deviceTotal = res.data.data || [];
      })
    },
    getCount2() {
      let params = {type: this.statisticType};
      getCount3(params).then(res => {
        let jcsj = res.data.data || [];
        let data = {};
        let yAxis = [];
        let series = [];
        //组装数据
        for (const jcsjKey in jcsj) {
          yAxis.push(jcsjKey.rwmc)
          series.push({name: jcsjKey.rwmc, value:jcsjKey.rwsl})
        }
        data.yAxis = yAxis;
        data.series = series;
        this.chart2 = data;
        console.log(555, this.chart2)
      })
    },
    fetchSYlbYxl() {
      let params = {type: this.statisticType};
      getSYlbYxl(params).then(res => {
        let dataList = res.data.data || [];
        // make new data list
        let newDataList = [];
        for (let i = 0; i < dataList.length; i++) {
          let newData = {};
          newData.value = dataList[i].total;  // value
          newData.name = dataList[i].ypdl;    // name
          newDataList.push(newData);
        }
        this.chart5.series.data = newDataList;

        // unused
        // this.chartList = data;
        // console.log(555, this.chartList);
        // no chart_box
        // this.$nextTick(() => {
        //   this.initNumChart1(data);
        // })

        // for test
        {
          let tmpDataList;
          if (this.statisticType == "year") {
            tmpDataList = [
              {
                "ypdl": "",
                "ypxl": "",
                "total": "859",
                "yxsl": "0",
                "rate": "0"
              },
              {
                "ypdl": "按年水果农残",
                "ypxl": "水果类",
                "total": "89",
                "yxsl": "0",
                "rate": "0"
              },
              {
                "ypdl": "按年畜肉兽残",
                "ypxl": "畜肉类",
                "total": "229",
                "yxsl": "0",
                "rate": "0"
              },
              {
                "ypdl": "按年蔬菜农残",
                "ypxl": "蔬菜类",
                "total": "299",
                "yxsl": "0",
                "rate": "0"
              }
            ];
          } else if (this.statisticType == "month") {
            tmpDataList = [
              {
                "ypdl": "",
                "ypxl": "",
                "total": "852",
                "yxsl": "0",
                "rate": "0"
              },
              {
                "ypdl": "按月水果农残",
                "ypxl": "水果类",
                "total": "82",
                "yxsl": "0",
                "rate": "0"
              },
              {
                "ypdl": "按月畜肉兽残",
                "ypxl": "畜肉类",
                "total": "222",
                "yxsl": "0",
                "rate": "0"
              },
              {
                "ypdl": "按月蔬菜农残",
                "ypxl": "蔬菜类",
                "total": "292",
                "yxsl": "0",
                "rate": "0"
              }
            ];
          } else if (this.statisticType == "day") {
            tmpDataList = [
              {
                "ypdl": "",
                "ypxl": "",
                "total": "85",
                "yxsl": "0",
                "rate": "0"
              },
              {
                "ypdl": "按日水果农残",
                "ypxl": "水果类",
                "total": "8",
                "yxsl": "0",
                "rate": "0"
              },
              {
                "ypdl": "按日畜肉兽残",
                "ypxl": "畜肉类",
                "total": "22",
                "yxsl": "0",
                "rate": "0"
              },
              {
                "ypdl": "按日蔬菜农残",
                "ypxl": "蔬菜类",
                "total": "29",
                "yxsl": "0",
                "rate": "0"
              }
            ];
          }

          // make new datalist
          let newDataList = [];
          for (let i = 0; i < tmpDataList.length; i++) {
            let tmpData = {};
            tmpData.value = tmpDataList[i].total;  // value
            tmpData.name = tmpDataList[i].ypdl;    // name
            newDataList.push(tmpData);
          }
          this.chart5.series.data = newDataList;
        }
        //-- end test

        // refresh
        const myChart5 = echarts.getInstanceByDom(this.$refs.chart5);
        const option5 = myChart5.getOption();
        option5.series[0].data = this.chart5.series.data;
        myChart5.setOption(option5);
      })
    },
    fetchSYjcJlb() {
      let params = {type: this.statisticType};
      getSYjcJlb(params).then(res => {
        let data = res.data.data || [];
        // this.initPie2(data)

        // 批次阳性数/总量
        // console.log(data);
      })
    },
    fetchSYjcSjb() {
      let params = {type: this.statisticType};
      getSYjcSjb(params).then(res => {
        let data = res.data.data || [];
        // this.initPie1(data)

        // 样品阳性数/总量
        // console.log(data);
      })
    },
    fetchSYsbXz() {
      let params = {type: this.statisticType};
      getSYsbXz(params).then(res => {
        let dataList = res.data.data || [];
        // 设备校准到期提醒
        // console.log(dataList);

        // this.remindList = dataList;
      })
    },
    fetchSYyxYj() {
      let params = {type: this.statisticType};
      getSYyxYj(params).then(res => {
        let dataList = res.data.data || [];
        // 滚动阳性预警
        // console.log(dataList);

        // make new data list
        let newDataList = [];
        for (let i = 0; i < dataList.length; i++) {
          let newData = {};
          newData.name = dataList[i].rq;  // name
          newData.title = dataList[i].xmmc;    // title
          newDataList.push(newData);
        }
        this.warnList = newDataList;
      })
    },
    fetchSySph() {
      let params = {type: this.statisticType};
      getSySph(params).then(res => {
        let data = res.data.data || [];
        // 本月本市检测执行排行榜
        // this.checkRank = data;
      })
    },
    fetchSYlbJcl() {
      let params = {type: this.statisticType};
      getSYlbJcl(params).then(res => {
        let data = res.data.data || [];
        // 本月本市检测执行排行榜

        const enumData = [
          {
            key: "name",
            value: '市场'
          },
          {
            key: "sl",
            value: 'sl'
          }];
        let formateData = enumData.map(({key, value}) => {
          return {
            name: value,
            data: new Array(data.length).fill('').map((_, index) => {
              return data[index][key];
            }),
          }
        })

        // this.initCheckChart([formateData[0].data, formateData[1].data]);
      })
    },

    initLine() {
      const chartDom = document.getElementById('line_box');
      const myChart = this.echarts.init(chartDom);
      const option = {
        title: {
          text: '任务量月度统计',
          left: 'center',
          top: '2%',
          textStyle: {
            color: '#04A7B3',
            fontWeight: 'normal',
            fontSize: '14px'
          },
        },
        grid: {
          containLabel: true,
          top: '25%',
          bottom: '3%',
        },
        xAxis: {
          type: 'category',
          data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月']
        },
        yAxis: {
          type: 'value'
        },
        color: ['#04A7B3'],
        series: [
          {
            data: [150, 230, 224, 218, 135, 147, 260],
            type: 'line',
            areaStyle: {
              opacity: 0.4,
              color: '#04A7B3'
            }
          }
        ]
      };
      option && myChart.setOption(option);
      window.addEventListener('resize', () => {
        myChart.resize();
      }, false);
    },
    initPie1(data) {
      const chartDom = document.getElementById('pie_box1');
      const myChart = this.echarts.init(chartDom);
      const option = {
        title: {
          text: '样品阳性数/总量',
          left: 'center',
          top: '2%',
          textStyle: {
            color: '#04A7B3',
            fontWeight: 'normal',
            fontSize: '14px'
          },
        },
        grid: {
          containLabel: true,
          top: '25%',
          bottom: '3%',
        },
        tooltip: {
          trigger: 'item'
        },
        color: ['#ef6567', '#04A7B3'],
        series: [
          {
            name: '样品阳性数/总量',
            type: 'pie',
            radius: '45%',
            center: ['50%', '55%'],
            data: [
              {value: 12, name: '阳性数'},
              {value: 735, name: '总量'},
            ],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      };
      option && myChart.setOption(option);

      window.addEventListener('resize', () => {
        myChart.resize();
      }, false);

    },
    initPie2(data) {
      const chartDom = document.getElementById('pie_box2');
      const myChart = this.echarts.init(chartDom);
      const option = {
        title: {
          text: '批次阳性数/总量',
          left: 'center',
          top: '2%',
          textStyle: {
            color: '#04A7B3',
            fontWeight: 'normal',
            fontSize: '14px'
          },
        },
        grid: {
          containLabel: true,
          top: '25%',
          bottom: '3%',
        },
        tooltip: {
          trigger: 'item'
        },
        color: ["#179f84", "#9AB958", "#EE7C00", "#BE382B", "#F4C40C", "#00AAFF", "#D9037C"],
        series: [
          {
            name: '样品阳性数/总量',
            type: 'pie',
            radius: ['30%', '50%'],
            center: ['50%', '55%'],
            data: [
              {value: 12, name: '阳性数'},
              {value: 50, name: '总量'},
            ],
          }
        ]
      };
      option && myChart.setOption(option);
      window.addEventListener('resize', () => {
        myChart.resize();
      }, false);
    },
    initCheckChart(data) {
      const chartDom = document.getElementById('check_chart');
      const myChart = this.echarts.init(chartDom);
      const option = {
        xAxis: {
          type: 'category',
          data: data[0]
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '快检仪',
            data: data[1],
            type: 'bar',
            stack: 'Ad',
            barWidth: 50,
            emphasis: {
              focus: 'series'
            },
          }
        ]
      };
      // const myChart = this.echarts.init(chartDom);
      // const option = {
      //   legend: {
      //     top: '15%'
      //   },
      //   title: {
      //     top: '5%',
      //     text: '本月不同类别检测量对比',
      //     left: 'center',
      //     textStyle: {
      //       color: '#04A7B3',
      //       fontWeight: 'normal',
      //       fontSize: '14px'
      //     },
      //   },
      //   grid: {
      //     containLabel: true,
      //     top: '25%',
      //     bottom: '5%'
      //   },
      //   color: ["#179f84", "#9AB958", "#EE7C00", "#BE382B", "#F4C40C", "#00AAFF", "#D9037C"],
      //   tooltip: {},
      //   dataset: {
      //     source: [
      //       ['product', '果蔬', '水产', '生鲜', '禽蛋', '加工', '阳性'],
      //       ['20日', 85, 35, 60, 75, 24, 1],
      //       ['21日', 81, 33, 55, 72, 21, 0],
      //       ['22日', 83, 31, 61, 71, 22, 2],
      //       ['23日', 82, 32, 62, 74, 25, 1],
      //       ['24日', 84, 35, 58, 70, 23, 1],
      //     ]
      //   },
      //   xAxis: { type: 'category' },
      //   yAxis: {},
      //   series: [
      //     { type: 'bar', barWidth: "15" },
      //     { type: 'bar', barWidth: "15" },
      //     { type: 'bar', barWidth: "15" },
      //     { type: 'bar', barWidth: "15" },
      //     { type: 'bar', barWidth: "15" },
      //   ]
      // };
      option && myChart.setOption(option);
      window.addEventListener('resize', () => {
        myChart.resize();
      }, false);
    },
    initNumChart1(data = []) {
      // console.log(3333, this.chartList)
      let chartDom = document.getElementsByClassName('chart_box'); // 对应地使用ByClassName
      for (var i = 0; i < chartDom.length; i++) { // 通过for循环，在相同class的dom内绘制元素
        const myChart = this.echarts.init(chartDom[i]);
        const option = {
          tooltip: {
            trigger: 'item'
          },
          graphic: [{ //环形图中间添加文字
            type: 'text', //通过不同top值可以设置上下显示
            left: 'center',
            top: 'center',
            style: {
              text: this.chartList[i].ypxl,
              textAlign: 'center',
              fill: '#000', //文字的颜色
              fontSize: 12,
              lineHeight: 16,
            }
          }, { //环形图中间添加文字
            type: 'text', //通过不同top值可以设置上下显示
            left: 'center',
            top: '92%',
            style: {
              text: `${this.chartList[i].yxsl}/${this.chartList[i].total}`,
              textAlign: 'center',
              fill: '#000', //文字的颜色
              fontSize: 12,
              lineHeight: 16,
            }
          }],
          color: ["#179f84", "#FFE209", "#EE7C00", "#BE382B", "#F4C40C", "#00AAFF", "#9AB958"],
          series: [
            {
              name: this.chartList[i].yxsl,
              type: 'pie',
              radius: ['60%', '80%'],
              label: {
                show: false,
                position: 'center'
              },
              emphasis: {
                label: {
                  show: false,
                  fontSize: 14,
                  fontWeight: 'bold'
                }
              },
              labelLine: {
                show: false
              },
              data: [
                {value: this.chartList[i].yxsl, name: '合格率'},
                {value: this.chartList[i].total - this.chartList[i].yxsl, name: '不合格率'},
              ]
            }
          ]
        };
        option && myChart.setOption(option);
        window.addEventListener('resize', () => {
          myChart.resize();
        }, false);
      }

    },
    initNumChart2() {
      const chartDom = document.getElementById('chart2');
      const myChart = this.echarts.init(chartDom);
      const option = {
        tooltip: {
          trigger: 'item'
        },
        graphic: [{ //环形图中间添加文字
          type: 'text', //通过不同top值可以设置上下显示
          left: 'center',
          top: 'center',
          style: {
            text: '果蔬类农药残',
            textAlign: 'center',
            fill: '#000', //文字的颜色
            fontSize: 12,
            lineHeight: 16,
          }
        }, { //环形图中间添加文字
          type: 'text', //通过不同top值可以设置上下显示
          left: 'center',
          top: '92%',
          style: {
            text: '231/235次',
            textAlign: 'center',
            fill: '#000', //文字的颜色
            fontSize: 12,
            lineHeight: 16,
          }
        }],
        color: ['#04A7B3', '#FFE209'],
        series: [
          {
            name: '果蔬农药残',
            type: 'pie',
            radius: ['60%', '80%'],
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: false,
                fontSize: 14,
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: [
              {value: 92, name: '合格率'},
              {value: 8, name: '不合格率'},
            ]
          }
        ]
      };
      option && myChart.setOption(option);
      window.addEventListener('resize', () => {
        myChart.resize();
      }, false);
    },
    initNumChart3() {
      const chartDom = document.getElementById('chart3');
      const myChart = this.echarts.init(chartDom);
      const option = {
        tooltip: {
          trigger: 'item'
        },
        graphic: [{ //环形图中间添加文字
          type: 'text', //通过不同top值可以设置上下显示
          left: 'center',
          top: 'center',
          style: {
            text: '果蔬类农药残',
            textAlign: 'center',
            fill: '#000', //文字的颜色
            fontSize: 12,
            lineHeight: 16,
          }
        }, { //环形图中间添加文字
          type: 'text', //通过不同top值可以设置上下显示
          left: 'center',
          top: '92%',
          style: {
            text: '231/235次',
            textAlign: 'center',
            fill: '#000', //文字的颜色
            fontSize: 12,
            lineHeight: 16,
          }
        }],
        color: ['#04A7B3', '#FFE209'],
        series: [
          {
            name: '果蔬农药残',
            type: 'pie',
            radius: ['60%', '80%'],
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: false,
                fontSize: 14,
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: [
              {value: 92, name: '合格率'},
              {value: 8, name: '不合格率'},
            ]
          }
        ]
      };
      option && myChart.setOption(option);
      window.addEventListener('resize', () => {
        myChart.resize();
      }, false);
    },
    initNumChart4() {
      const chartDom = document.getElementById('chart4');
      const myChart = this.echarts.init(chartDom);
      const option = {
        tooltip: {
          trigger: 'item'
        },
        graphic: [{ //环形图中间添加文字
          type: 'text', //通过不同top值可以设置上下显示
          left: 'center',
          top: 'center',
          style: {
            text: '果蔬类农药残',
            textAlign: 'center',
            fill: '#000', //文字的颜色
            fontSize: 12,
            lineHeight: 16,
          }
        }, { //环形图中间添加文字
          type: 'text', //通过不同top值可以设置上下显示
          left: 'center',
          top: '92%',
          style: {
            text: '231/235次',
            textAlign: 'center',
            fill: '#000', //文字的颜色
            fontSize: 12,
            lineHeight: 16,
          }
        }],
        color: ['#04A7B3', '#FFE209'],
        series: [
          {
            name: '果蔬农药残',
            type: 'pie',
            radius: ['60%', '80%'],
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: false,
                fontSize: 14,
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: [
              {value: 92, name: '合格率'},
              {value: 8, name: '不合格率'},
            ]
          }
        ]
      };
      option && myChart.setOption(option);
      window.addEventListener('resize', () => {
        myChart.resize();
      }, false);
    },
    handleChange(val) {
      window.console.log(val);
    },
    //三级ai接口
    getCount10() {
      const params={
        type:this.statisticType
      }
      getCount10(params).then(res => {
        console.log(res,'rsss')
        // let data = res.data.data || [];
        // this.initNumChart10(data);
      })
    }
  },
};
</script>

<style scoped lang="less">
.el-table {
  --el-table-border-color: rgba(255, 255, 255, 0.1);
  --el-table-header-bg-color: rgb(15, 23, 42);
}
.el-table__body tr:hover > td {
  background-color: rgba(5, 103, 110, 0.7) !important;
}
.main{
  background: rgb(5, 32, 67);
}
.right_top {
  width: 100%;
  height: 170px;
  display: flex;
  justify-content: space-between;

  .right_pie {
    width: 47%;
    height: 100%;
    background: #F3F6F9;
    border: 1px solid #ccc;
  }
}

.right_center {
  width: 100%;
  height: 260px;
  background: #F3F6F9;
  border: 1px solid #ccc;
  margin-top: 15px;
  overflow: hidden;

  .title_div {
    width: 100%;
    height: 35px;
    line-height: 35px;
    text-align: center;
    border-bottom: 1px dotted #ccc;
    color: #04A7B3;
  }

  .title_div2 {
    color: rgba(249, 165, 39, 1);
  }

  .item_div {
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 0 10px;
    color: #04A7B3;
    margin-top: 8px;
  }

  .item_div2 {
    color: rgba(249, 165, 39, 1);
  }
}

.right_line {
  width: 100%;
  height: 300px;
  background: #F3F6F9;
  border: 1px solid #ccc;
  margin-top: 15px;
}

.center_bottom {
  width: 100%;
  height: 400px;
  margin-top: 10px;
}

.center_center {
  width: 100%;
  height: 400px;
  background: #F3F6F9;
  border: 1px solid #ccc;
}

.center_top {
  width: 100%;
  display: flex;

  .notice_box {
    width: calc(100% - 120px);
    height: 105px;
    overflow: auto;
    background: rgba(4, 167, 179, 0.2);
    margin-left: 10px;
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;

    .notice_item {
      display: flex;
      align-items: center;
      color: rgba(4, 167, 179, 1);
      padding: 0 10px;

      img {
        width: 15px;
        height: auto;
        margin-right: 5px;
      }
    }
  }

  .today_box {
    width: 105px;
    height: 105px;
    background: #04A7B3;
    display: flex;
    flex-direction: column;
    color: #fff;
    justify-content: center;
    align-items: center;

    .day_box {
      font-size: 28px;
      margin-bottom: 10px;
    }
  }
}

.chart_box {
  width: 50%;
  height: 200px;
  padding: 10px;
  box-sizing: border-box;
}

.rate_img {
  width: 18px;
  height: auto;
  position: relative;
  top: 3px;
}

.check_title {
  width: 100%;
  color: #04A7B3;
  text-align: center;
  font-size: 14px;
}

.check_box {
  width: 100%;
  background: #F3F6F9;
  padding: 20px 10px;
  border: 1px solid #ccc;

  .pie_box {
    width: 100%;
    display: flex;
    flex-flow: row wrap;
  }
}

.total_item {
  width: 100%;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px;
  font-size: 14px;
  border: 1px solid #094F6C;
  border-radius: 5px;
  color: #A6A3A3;

  img {
    width: 25px;
    height: auto;
  }
}

.el-font-size {
  font-size: 14px;
}

.el-row {

  &:last-child {
    margin-bottom: 0;
  }
}

.el-col {
  border-radius: 4px;
}

.bg-purple-dark {
  background: #99a9bf;
}

.bg-purple {
  background: #d3dce6;
}

.bg-purple-light {
  background: #e5e9f2;
}

.grid-content {
  border-radius: 4px;
  min-height: 36px;
}

.row-bg {
  padding: 10px 0;
  background-color: #f9fafc;
}

.left2 {
  border: 1px solid #084E6C;
  height: 120px;
  border-radius: 5px;
  font-size: 0.7vw;
}

.leftTitle {
  color: #04A7B3;
  margin-left: 10px;
  margin-right: 10px;
  margin-top: 10px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: rgb(240, 206, 9);
}

.left3 {
  border: 1px solid #084E6C;
  height: 280px;
  border-radius: 5px;
  margin-top: 20px;
  padding-top: 10px;
}

.left3T {
  width: 100%;
  text-align: center;
  color: #04A7B3;
  margin-top: 10px;
  font-size: 14px;
}

.left4T {
  width: 100%;
  text-align: center;
  color: #04A7B3;
  margin-top: 10px;
  font-size: 14px;

}

.left4 {
  border: 1px solid #084E6C;
  height: 410px;
  border-radius: 5px;
  margin-top: 20px
}

.main {
  position: relative;
  margin-top: -40px
}

.moon {
  background-color: #262424;
}


.lighr {
  background-color: #fff;
}

.right1 {
  border: 1px solid #084E6C;
  border-radius: 5px;
  height: 200px;
}

.right2 {
  border: 1px solid #084E6C;
  border-radius: 5px;
  height: 200px;
}

.right4 {
  border: 1px solid #084E6C;
  margin-top: 20px;
  border-radius: 5px;
  height: 520px;
  margin-bottom: 30px;
}

.rightT2 {
  width: 100%;
  text-align: center;
  font-size: 14px;
  height: 30px;
  line-height: 30px;

}

.rightT21 {
  border-bottom: 1px dashed #203572;
  padding-bottom: 34px;
  color: #04A7B3;

}

.rightT22 {
  color: #999;
  border-bottom: 1px dashed #999;
  padding-bottom: 34px;
}

.text1 {
  width: 65%;
  min-width: 65%;
  color: #04A7B3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-left: 10px;
  height: 30px;
  line-height: 30px;
  display: inline-block;
  font-size: 0.7vw;
}

.text2 {
  color: #04A7B3;
  margin-right: 10px;
  height: 40px;
  margin-left: 20px;
  font-size: 14px;
  position: absolute;
  margin-top: 4px;
  font-size: 0.7vw;
}

.tips {
  position: absolute;
  color: #A6A3A3;
  margin-top: -160px;
  float: right;
  right: 4%;
  font-size: 0.8vw;
}

.center {
  border: 1px solid #084E6C;
  margin-top: 10px;
  border-radius: 5px;
  height: 325px;
  margin-bottom: 30px;
}

.title2 {
  width: 50%;
  margin-left: 25%;
  text-align: center;
  color: #04A7B3;
  font-size: 30px;
  height: 80px;
  line-height: 80px;
  margin-top: 40px;
}

.time {
  position: absolute;
  color: #A6A3A3;

  height: 80px;
  line-height: 80px;
}

.action {
  float: right;
  color: red;
  height: 80px;
  line-height: 80px;
  position: absolute;
  right: 160px;
  margin-top: -80px;
  display: flex;
}

.img1 {
  position: absolute;
  float: right;
  width: 20px;
  height: 30px;
  margin-left: 240px;
  margin-top: 24px;
}

.img1 {
  position: absolute;
  float: right;
  width: 20px;
  height: 20px;
  margin-left: 240px;
  margin-top: 30px;
}

.img2 {
  position: absolute;
  float: right;
  width: 20px;
  height: 20px;
  margin-left: 270px;
  margin-top: 30px;
}

.img3 {
  position: absolute;
  float: right;
  width: 20px;
  height: 20px;
  margin-left: 300px;
  margin-top: 30px;
}

.ditu {
  border: 1px solid #084E6C;
  border-radius: 5px;
  background-image: url('../../assets/img/300.jpg');
  background-size: 100% 100%;
  background-repeat: no-repeat
}

.flex {
  display: flex;
}

.item-center {
  align-items: center;
}

.ml-10 {
  margin-left: 10px;
}
</style>
